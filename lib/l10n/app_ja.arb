{"@@locale": "ja", "followerSystemLanguage": "システム言語に追従", "simplifiedChinese": "中国語簡体", "traditionalChinese": "中国語繁体", "english": "英語", "japanese": "日本語", "globalOptSuccess": "操作が成功しました", "globalConfirm": "確認", "globalCancel": "キャンセル", "globalSetting": "設定へ移動", "globalOk": "OK", "globalRecommend": "おすすめ", "globalSubmit": "送信", "globalSave": "保存", "globalClose": "閉じる", "globalDelete": "削除", "globalDeleteConfirm": "本当に削除しますか？", "globalDeleteSuccess": "削除しました", "globalReadImageFail": "画像読み込み失敗", "globalClear": "クリア", "globalSuccessful": "成功", "appName": "<PERSON><PERSON><PERSON>", "refresh": "更新", "emptyDataMessage": "データがありません", "listScrollHasMore": "さらに読み込む...", "listScrollNoMore": "最後まで到達しました", "listLoadFailed": "データ取得失敗", "listScrollRefreshSuccess": "更新成功", "noNetwork": "通信エラー", "accountPasswordError": "ログイン失敗：パスワードを確認してください", "signupPhonePlaceholder": "電話番号", "signupPhoneErrorRequired": "正しい電話番号を入力してください", "signupPhoneErrorFormat": "電話番号形式が不正です", "signupEmailPlaceholder": "メールアドレス", "signupEmailErrorRequired": "正しいメールアドレスを入力してください", "signupEmailErrorFormat": "メールアドレス形式が不正です", "signupPasswordPlaceholder": "パスワード", "signupPasswordErrorRequired": "パスワードを入力してください", "signupPasswordLengthError": "パスワードは最低{length}文字必要です", "signupConfirmPasswordPlaceholder": "パスワード確認", "signupConfirmPasswordErrorRequired": "パスワードを再入力してください", "signupConfirmPasswordErrorMatch": "パスワードが一致しません", "signupInvitationPlaceholder": "招待コード（任意）", "signupAgreementFrag1": "私は", "signupAgreementFrag2": "および", "signupAgreementService": "《利用規約》", "signupAgreementPolicy": "《プライバシーポリシー》", "signupAgreementServiceTitle": "利用規約", "signupAgreementPolicyTitle": "プライバシーポリシー", "signupSignupButton": "新規登録", "signupSwitchLoginDesc": "既にアカウントをお持ちですか？", "signupSwitchLoginBtn": "ログイン", "signupGoogleSignupButton": "Googleで続ける", "signupGoogleLoginButton": "Google", "signupVerifyCodeTitle": "メール認証", "signupVerifyCodeDesc": "{account}に認証コードを送信しました（15分間有効）", "signupEmailExists": "既に登録済みのメールです", "signupFindPasswordSuccessTitle": "メール送信完了", "signupFindPasswordSuccessFg1": "パスワード再設定用メールを送信しました", "signupFindPasswordSuccessFg2": "ご確認ください", "signupResetPasswordSuccessMsg": "パスワード再設定完了", "signupVerifyCodeHint": "認証コード", "sendCodeFailedDesc": "認証コード送信失敗", "signupFailedDesc": "登録失敗：パスワードを確認してください", "secondsRemaining": "{second}秒", "humanVerifyErrorRequired": "認証を完了してください", "findPasswordMainButton": "パスワード再設定", "findPasswordLoginDesc": "既にアカウントをお持ちですか？", "findPasswordLoginSignin": "ログイン", "resetPasswordNoCode": "認証コードが見つかりません", "resetPasswordResetButton": "パスワード再設定", "resetPasswordSuccess": "パスワード再設定完了", "resetPasswordFailed": "パスワード再設定失敗", "googleLoginButtonHr": "または", "loginLoginButton": "ログイン", "loginForgot": "パスワードをお忘れですか？", "signupByEmail": "メール登録", "loginByEmail": "メールログイン", "loginByPhone": "電話番号ログイン", "loginTitle": "ログイン", "loginOr": "または", "register_new_account": "新規アカウント登録", "loginSubTitle": "電話番号またはメールアドレスを入力", "tabbarConversations": "チャット", "tabbarContacts": "連絡先", "tabbarMine": "マイページ", "chatMessageUnknown": "未知のメッセージ形式：アプリを更新してください", "dateTimeYesterday": "昨日", "systemNotifyUser": "システム通知", "fileHelperUser": "ファイル転送アシスタント", "functionTitle": "機能説明", "functionFileHelperTips": "PC版でログイン後、ファイル転送が可能になります", "functionSystemTeamTips": "公式チームアカウント", "sendMessage": "メッセージ送信", "contactRemark": "備考設定", "contactRemarkSuccess": "変更しました", "contactSource": "ソース", "contactSourceUnknown": "不明", "scan": "スキャン", "addFriend": "友達追加", "webviewOpenInBrowser": "ブラウザで開く", "webviewRefresh": "再読み込み", "webviewCleanBrowsingData": "キャッシュ削除", "applyFriend": "友達申請", "inputRemark": "備考を入力", "applyed": "申請済み", "personalInfo": "個人情報", "avatar": "プロフィール画像", "name": "名前", "shortNo": "{appName} ID", "myQrcode": "マイQRコード", "inviteCode": "招待コード", "mobile": "電話番号", "gender": "性別", "male": "男性", "female": "女性", "qrDesc": "QRコードをスキャンして{appName}で追加", "search": "検索", "myAppId": "マイ{appName} ID：{shortNo}", "scanUserQr": "QRコードをスキャン", "phone": "携帯", "web": "Web", "pc": "PC", "online": "オンライン", "offline": "オフライン", "lastSeenTime": "最終オンライン", "justNow": "たった今", "minAgo": "{min}分前", "userRevokeMsg": "「{user}」がメッセージを撤回", "managerRevokeUserMsg": "「{user}」のメッセージを撤回しました", "managerRevokeUserMsg1": "「{user}」がメンバーのメッセージを撤回", "myRevokeMsg": "メッセージを撤回しました", "you": "あなた", "newsLine": "新しいメッセージ", "lastMsgChatRecord": "[チャット記録]", "chatRecord": "チャット履歴", "chatTitleRecords": "{user}のチャット履歴", "groupChat": "グループチャット", "userCard": "プロフィールカード", "chooseCountry": "国/地域選択", "sendCode": "認証コード送信", "image": "画像", "card": "名刺", "alertDialogTitle": "確認", "imagePick": "写真を選択", "imageTakePhoto": "写真を撮る", "imageGallery": "アルバム", "deleteFriends": "友達解除", "pushBlackList": "ブロック", "pullOutBlackList": "ブロック解除", "blackListDesc": "ブロックされました：メッセージを受信しません", "deleteFriendsTips": "{name}を削除（チャット履歴も削除）", "joinBlackListTips": "ユーザーをブロックしますか？", "pullOutBlackListTips": "ブロックを解除しますか？", "nickname": "表示名：{name}", "pressTalk": "押して話す", "releaseToCancel": "離してキャンセル", "holdToRecord": "上にスワイプでキャンセル", "timeRemaining": "残り時間：{time}", "newFriends": "新しい友達", "savedGroups": "保存済みグループ", "blackFriends": "ブロックリスト", "requestAddFriend": "友達申請", "agreeApply": "承認", "agreedApply": "承認済み", "msgSetTop": "チャットを固定", "msgCancelTop": "固定を解除", "openChannelNotice": "通知をオン", "closeChannelNotice": "通知をオフ", "resendMsgTip": "送信失敗：再送しますか？", "resendBlacklistGroup": "ブロック中：再送しますか？", "resendBlacklistUser": "ブロックユーザー：再送しますか？", "resendNoRelationUser": "友達ではない：再送しますか？", "resendNoRelationGroup": "グループ外：再送しますか？", "msgSendFail": "送信失敗", "msgSendFailResend": "再送", "statusConnecting": "接続中...", "statusSuccess": "接続済み", "statusKicked": "他端末でログイン済み", "statusNoNetwork": "ネットワークエラー", "statusSyncMsg": "同期中", "statusFail": "未接続", "statusSyncCompleted": "同期完了", "chooseContact": "連絡先を選択", "startGroupChat": "グループ作成", "signalDecryptErr": "[暗号化メッセージ]", "contentFormatErr": "[形式エラー]", "noRelationRequest": "{name}の承認が必要です", "unknowMsgType": "[不明なメッセージ]", "renameSuccess": "名前を変更しました", "inviteLink": "招待リンク", "categoryOfficial": "公式", "categoryCustomerService": "サポート", "categoryVisitor": "ビジター", "categoryAllStaff": "全メンバー", "categoryDepartment": "部署", "categoryBot": "ボット", "categoryCommunity": "コミュニティ", "chatInfo": "チャット情報", "groupOwner": "グループオーナー", "groupManager": "管理者", "showAllMembers": "全メンバー表示", "groupMembers": "メンバー", "editGroupNotice": "オーナー/管理者のみ編集可", "deleteGroupMembers": "メンバー削除", "addGroupMembers": "メンバー追加", "myInGroupName": "グループ内表示名", "updateInGroupName": "グループ内専用表示名を設定", "groupName": "グループ名", "groupQr": "グループQRコード", "groupCard": "グループ情報", "groupAnnouncement": "お知らせ", "deleteGroup": "退会して削除", "clearChatMsg": "履歴削除", "searchChatMsg": "履歴検索", "msgRemind": "通知をミュート", "msgTop": "固定チャット", "saveToMaillist": "連絡先に保存", "showRemarkName": "表示名を表示", "myRemarkNameInGroup": "グループ内表示名", "unsetting": "未設定", "deleteMessages": "メッセージ削除", "deleteChat": "チャット削除", "deleteConverMsgTips": "自分の端末のみ削除されます", "clearHistory": "履歴を消去", "clearHistoryTip": "{name}とのチャット履歴を削除しますか？", "exitGroupTips": "グループから退出しますか？", "groupQrDesc": "QRコードの有効期限：{day}日（{date}）", "noSavedGroup": "「連絡先に保存」で追加できます", "remark": "備考", "remarkDesc": "自分だけが見える備考", "enterGroupName": "グループ名入力", "invitationSent": "招待を送信しました", "lastMsgDraft": "[下書き]", "lastMsgRemind": "[メンションあり]", "lastMsgCard": "[名刺]", "applyJoinGroup": "[参加申請]", "clientWeb": "Web版", "newMessageNotification": "新着通知", "universal": "一般", "takePhoto": "写真撮影", "selectFromPhotoAlbum": "アルバムから選択", "saveToPhotoAlbum": "写真に保存", "clearCacheMemory": "メディアキャッシュ削除", "mulitLanguage": "言語設定", "versionInfo": "バージョン情報", "userAgreement": "利用規約", "privacyPolicy": "プライバシーポリシー", "logOut": "ログアウト", "webUrl": "Web版URL", "webSide": "Web", "appWeb": "{appName} Web版", "visitWebTip": "ブラウザで{appName}にアクセスし、QRコードをスキャンしてください（推奨ブラウザ：Chrome, Firefox）", "qrScanLogin": "QRコードログイン", "copySuccess": "クリップボードにコピー", "mentionAll": "全員へ", "replyMsgIsRevoked": "元メッセージが撤回されました", "msgRevoked": "撤回されたメッセージ", "copy": "コピー", "delete": "削除", "revoke": "撤回", "forward": "転送", "reply": "返信", "recognize": "テキストに変換", "multipleChoice": "複数選択", "copied": "コピー済み", "pushText": "テキスト", "msgImage": "[画像]", "msgVoice": "[音声]", "msgVideo": "[動画]", "msgLocation": "[位置情報]", "msgCard": "[名刺]", "msgFile": "[ファイル]", "msgGif": "[GIF]", "msgSticker": "[表情]", "msgUnknown": "[未知]", "searchMoreContacts": "連絡先をさらに表示", "searchMoreGroups": "グループをさらに表示", "searchMoreMessage": "メッセージをさらに表示", "searchMessageCount": "該当メッセージ：{count}件", "searchMessage": "メッセージ履歴", "searchRecent": "最近のチャット", "searchGroup": "グループ", "searchContact": "連絡先", "globalSearch": "検索", "cannotReplyMsg": "ミュート中：返信不可", "cannotEditMsg": "ミュート中：編集不可", "welcomeToGleezy": "{name}へようこそ", "chatAnytime": "楽しい時間、心地よい会話", "forwardSuccess": "転送しました", "itemForward": "個別転送", "itemForwardCount": "[{count}件を転送]", "mergeForward": "まとめて転送", "deleteSelectMsgTip": "端末内のみ削除されます", "maxChooseMsgCount": "最大{count}件まで選択", "onlyCharactersAndNumbers": "英数字のみ使用可", "moreThanCharacters": "{count}文字以上必要", "lessThanCharacters": "{count}文字以内", "tapAndAgree": "「登録」で", "and": "および", "hadAccount": "既にアカウントをお持ちですか？", "registerGoLogin": "登録済み：ログインしますか？", "modifySuccess": "変更しました", "modifyFail": "更新失敗", "avatarSaved": "プロフィール画像を保存しました", "saveFailed": "保存失敗", "avatarEditing": "プロフィール画像編集", "needAlbumPermission": "写真へのアクセス許可が必要です", "openAlbumPermission": "設定で写真アクセスを許可してください", "noAvatar": "プロフィール画像を設定してください", "sendTo": "送信先：", "reEdit": "再編集", "report": "報告", "chooseChat": "チャットを選択", "securityAndPrivacy": "セキュリティとプライバシー", "resetPassword": "パスワード変更", "personalInfoCollection": "個人情報収集リスト", "destroyAccount": "アカウント削除", "destoryFailedDesc": "削除に失敗しました", "destoryVerifyCodeHint": "認証コード", "makeSureDestoryAccount": "アカウント削除の確認", "destoryAccountTip": "この操作は取り消せません！", "destory": "削除", "makeSureLogout": "ログアウトの確認", "logoutTip": "現在のアカウントからログアウトしますか？", "loginExpired": "セッション切れ。再ログインが必要です", "destorySuccess": "アカウント削除完了！", "generalSettings": "一般設定", "needVoicePermission": "マイクアクセス許可が必要です", "openVoicePermission": "マイクアクセスを有効にしてください", "settingSound": "サウンド", "settingShock": "振動", "smsSendTooFrequently": "送信回数が多すぎます", "verifyCodeError": "認証コードエラー", "completeUserInfo": "個人情報を完成", "bindPhoneNumber": "電話番号を紐付けてください", "videoCall": "ビデオ通話", "audioCall": "音声通話", "phoneCall": "電話", "audioCallWaiting": "音声通話を待っています", "audioCallIncoming": "音声通話の招待を受信しました", "videoCallWaiting": "通話を待機中", "videoCallIncoming": "着信中", "videoCallReject": "拒否", "videoCallAccept": "応答", "networkError": "ネットワークエラー、後でもう一度お試しください", "callEnded": "通話が終了しました", "joinCallFailed": "通話に参加できませんでした、後でもう一度お試しください", "hangupCallFailed": "通話を終了できませんでした、後でもう一度お試しください", "me": "私", "emptyPhoneNumber": "電話番号が設定されていません", "switchAccount": "アカウントを切り替える", "file": "ファイル", "downloadFail": "ファイルのダウンロードに失敗しました", "fileSize": "ファイル サイズ: {size}", "download": "ダウンロード", "openFile": "他のアプリケーションで開く", "noAppToOpen": "アプリケーションを処理できません", "accountNotExist": "アカウントが存在しません", "onlyModifyOnce": "一度のみ変更可能です", "shortNoRule": "4～8文字（アルファベット/数字）、一度だけ変更可能", "signature": "個性サイン", "signatureLimit": "255个字符以内", "inviteNoRule": "4～8文字（アルファベット/数字）、一度だけ変更可能", "clearSuccess": "削除が成功しました", "saveSuccess": "保存が成功しました", "inputVerifyCode": "認証コードを入力してください", "messageDeleted": "メッセージは削除されました", "shareToConversation": "友達に送信", "shareSuccess": "送信成功", "saveFile": "保存", "fileSaved": "ファイルは {path} に保存されました", "saveToGallerySuccess": "アルバムに保存されました", "groupSource": "グループに入る方法", "inviteGroup": "グループに招待します", "sayHello": "こんにちは", "sayHelloTip": "こんにちはと言ってチャットを開始してください", "sayHelloGroupRemark": "こんにちは、私はグループ チャット \"{groupName}\" の {userName} です", "sayHelloRemark": "こんにちは、私は {userName}", "sayHelloSuccess": "メッセージが送信されました", "applyGroupFriendTip": "新しい友達申請を送信します", "applyGroupFriendRemark": "私はグループ チャット \"{groupName}\" の {userName} です", "applyFriendRemark": "私は {userName} です", "applyFriendFailed": "友達申請に失敗しました", "applyExpired": "期限切れ", "messageRecalled": "このメッセージは取り消されました", "cropImage": "画像をトリミング", "inCall": "通話中", "missedCall": "未接電話", "noValidQrcode": "有効なQRコードがありません", "anonymous": "匿名", "connectFailed": "接続に失敗しました。再接続しますか？", "connectNoNetwork": "ネットワークに接続できません。", "reconnect": "再接続", "invitorNotExist": "招待コードが存在しません", "joinGroupMemberCount": "(合計 {count} 人)", "joinGroupSubmit": "このグループチャットに参加します", "joinGroupFail": "グループ チャットに参加できませんでした!", "foundNewVersion": "新しいバージョンが見つかりました!", "upgradeNow": "今すぐアップデートしてください!", "skip": "スキップ", "downloading": "ダウンロード中...", "forceUpgradeTitle": "アプリケーションのアップグレード", "forceUpgradeInfo": "APP のバージョンが低すぎることが検出されました\n使用する前に最新バージョンにアップグレードしてください", "noUpgrade": "アップデートは検出されませんでした", "needCameraPermission": "カメラの権限が必要です", "enableCameraPermission": "設定でカメラの権限を有効にしてください", "enableAudioPermission": "設定でマイクの権限を有効にしてください", "forbidden": "ミュート", "forbiddenCanNotResend": "ミュート中はメッセージを再送信できません", "systemMsg": "システムメッセージ", "anonymousForbidForward": "匿名グループではメッセージの転送が禁止されています", "phoneNumberExists": "電話番号は既に存在します", "bindEmail": "メールアドレスを紐づける", "emailExists": "メールアドレスは既に存在します", "callCanceled": "キャンセルしました", "callNoAnswer": "応答なし", "callRejected": "拒否されました", "callCanceledByPeer": "相手がキャンセルしました", "callMissed": "不在着信", "callReject": "拒否しました", "callDuration": "通話時間", "callFailed": "通話に失敗しました。ネットワークを確認してください", "pinned": "ピン留め", "pinnedBy": "{name}がピン留めしました", "msgUnpinned": "ピン留めを解除", "msgCannotPin": "このメッセージはピン留めできません", "caringMode": "思いやりのあるモデル", "caringModeSummary": "「予約モード」をオンにすると、次の機能が選択できるようになります:", "caringModeSummary1": "・テキストが大きくなり、ボタンも大きくなります;", "turnOn": "オンにする", "turnOff": "オフにする", "restartApp": "アプリケーションを再起動します", "restartAppTip": "アプリケーションを再度開くには、ここをクリックしてください。 ", "caringTurnOn": "オンになりました", "uploadSuccess": "アップロード成功", "uploadFailed": "アップロードに失敗しました", "deleteFailed": "削除に失敗しました", "finish": "完了", "groupAdmin": "グループ管理者", "addGroupAdmin": "管理者を追加", "deleteGroupAdmin": "グループ管理者を削除", "globalAdd": "追加", "msgPinConfirm": "このメッセージをピン留めしますか？", "msgPinWithPeer": "{name} のためにもピン留めする", "msgCannotUnpin": "このメッセージのピン留めを解除できません", "selectedCount": "{count}人を選択", "confirmCount": "確認({count})", "clearHistoryBoth": "相手のチャット履歴も同時に削除", "phoneNumberNotInTargetRegion": "電話番号が対象地域に属していません", "dragSliderToCompletePuzzle": "スライダーをドラッグしてパズルを完成させてください", "verificationSuccess": "検証成功", "verificationFailedTryAgain": "検証に失敗しました、再試行してください", "addSticker": "ステッカーを追加する", "addStickerSuccess": "正常に追加されました", "addStickerFailed": "絵文字の追加に失敗しました", "enableAnonymousTip": "匿名グループを有効にする", "sliderVerify": "スライド認証", "globalConfirmDelete": "削除を確認", "dateTimeToday": "今日", "shareToSelf": "自分に送信", "shareToFriendsOrGroups": "友達またはグループに送信", "sharedFromExternalApp": "外部アプリから共有", "allowSearchByPhone": "携帯電話番号での検索を許可します", "allowSearchByShort": "ショートナンバーでの検索を許可します", "groupNickname": "グループニックネーム: {name}", "containMemberName": "含む: {name}", "groupChannelName": "グループ名: {name}", "previewEffect": "プレビュー効果", "chatMessagePreview": "チャットメッセージプレビュー例", "smallTextPreview": "小さいテキストプレビュー例", "fontSize": "フォントサイズ"}