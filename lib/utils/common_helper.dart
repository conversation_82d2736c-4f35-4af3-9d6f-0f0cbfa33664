import 'dart:io';

import 'package:and/app.dart';
import 'package:and/cache/cache_helper.dart';
import 'package:and/common/utils/easy_loading_helper.dart';
import 'package:and/constant/common_keys.dart';
import 'package:and/constant/wk_cmd_keys.dart';
import 'package:and/http/http_config.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/l10n/language_utils.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/model/extension/wx_channel_member_ext.dart';
import 'package:and/model/user_login_info.dart';
import 'package:and/module/user/apply/apply_user_page.dart';
import 'package:and/module/user/sayhello/say_hello_page.dart';
import 'package:and/module/webview/in_app_browser.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/utils/notification_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:restart_app/restart_app.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

import 'dialog_utils.dart';
import 'http_utils.dart';
import 'im/im_utils.dart';
import 'scan_utils.dart';

class CommonHelper {
  static final CommonHelper _instance = CommonHelper._();

  static CommonHelper get instance => _instance;

  CommonHelper._();

  static String getAvatarUrl(String uid,
      {int channelType = WKChannelType.personal, bool anonymous = false}) {
    if (uid.isEmpty || uid.endsWith(WKCMDKeys.wkCMDEnd)) {
      return "";
    }
    if (uid == "-1") {
      return ImagePath.ic_mention;
    }
    var type = "users";
    if (channelType == WKChannelType.group) {
      type = "groups";
    }
    return "${HttpConfig.getApiUrl()}/$type/$uid/${anonymous ? "anonymous_avatar" : "avatar"}";
  }

  static String getFileUrl(String path) {
    if (path.isEmpty) {
      return "";
    }
    if (path.startsWith("http")) {
      return path;
    }
    return "${HttpConfig.getApiUrl()}/$path";
  }

  static String getMyAvatarUrl() {
    return getAvatarUrl(CacheHelper.userProfile?.uid ?? '');
  }

  static Future<void> init() async {
    if (isLogin()) {
      await IMUtils.initIM();
    }
  }

  static Future<void> loginAndLaunchHome(UserLoginInfo userInfo) async {
    CacheHelper.saveUserProfile(userInfo);
    CacheHelper.saveSyncFriend(true);

    /// 登录成功有devicetoken同步到服务端，没有的话取一下，取不到清空下服务端值。
    if (CacheHelper.deviceToken != null &&
        CacheHelper.deviceToken!.isNotEmpty) {
      HttpUtils.uploadDeviceToken(CacheHelper.deviceToken ?? "");
    }
    var result = await IMUtils.initIM();
    if (result) {
      await Get.offAllNamed(RouteGet.main);
    }
  }

  static Future<void> exitLogin({bool isDeleteToken = true}) async {
    if (CacheHelper.token == null || CacheHelper.token!.isEmpty) {
      return;
    }
    if (isDeleteToken) {
      await EasyLoadingHelper.show(onAction: () async {
        await HttpUtils.deleteDeviceToken();
      });
    }
    WKIM.shared.connectionManager.disconnect(true);
    CacheHelper.clearAll();
    Get.offAllNamed(RouteGet.main);
  }

  static bool isLogin() {
    return CacheHelper.userProfile?.token != null;
  }

  static void launchInAppBrowser(
    String url, {
    LaunchMode mode = LaunchMode.platformDefault,
    bool showTitle = false,
    Map<String, String> params = const {},
  }) async {
    Uri uri = Uri.parse(url);
    uri = uri.replace(queryParameters: {...uri.queryParameters, ...params});
    if (await canLaunchUrl(uri)) {
      await launchUrl(
        uri,
        mode: mode,
        browserConfiguration: BrowserConfiguration(showTitle: showTitle),
      );
    } else {
      if (kDebugMode) {
        print("'Could not launch $url'");
      }
    }
  }

  static Future<void> launchInWebView(
    String url, {
    String? title,
    String? callbackUrl,
    AppBarType? appBarType,
    bool showOption = true,
    Map<String, String> params = const {},
  }) async {
    Map<String, String> mutableParams = Map.from(params);
    mutableParams["language"] = LanguageUtils.language();
    Uri uri = Uri.parse(url.replaceAll("http://", "https://"));
    uri = uri
        .replace(queryParameters: {...uri.queryParameters, ...mutableParams});
    Get.to(CustomInAppBrowser(
      title: title,
      uri: uri,
      callbackUrl: callbackUrl,
      appBarType: appBarType,
      showOption: showOption,
    ));
  }

  static void report(Map<String, String> params) {
    CommonHelper.launchInWebView(HttpConfig.getWebUrl() + "/report.html",
        params: params);
  }

  static void launchEmail(String email) async {
    Uri uri = Uri.parse("mailto:$email");
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      if (kDebugMode) {
        print("'Could not launch $email'");
      }
    }
  }

  static void launchPhone(String phone) async {
    Uri uri = Uri.parse("tel:$phone");
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      if (kDebugMode) {
        print("'Could not launch $phone'");
      }
    }
  }

  static void scanContact() async {
    var barcode = await Get.toNamed(RouteGet.scan);
    if (barcode != null) {
      ScanUtils.handle(barcode);
    }
  }

  static void addFriend(BuildContext context,
      {required String channelID, String? vercode, String? groupNo, int? vercodeType}) async {
    var remark =
        context.l10n.applyFriendRemark(CacheHelper.userProfile?.name ?? '');
    if (groupNo != null) {
      var groupInfo = await WKIM.shared.channelManager
          .getChannel(groupNo, WKChannelType.group);
      if (groupInfo != null) {
        remark = context.l10n.applyGroupFriendRemark(
            groupInfo.channelName, CacheHelper.userProfile?.name ?? '');
      }

      if (vercode?.isEmpty ?? true) {
        var member = await WKIM.shared.channelMemberManager
            .getMember(groupNo, WKChannelType.group, channelID);
        if (member?.vercode?.isNotEmpty ?? false) {
          vercode = member?.vercode;
        }
      }
    }

    Get.to(ApplyUserPage(
        channelId: channelID,
        vercode: vercode,
        remark: remark,
        vercodeType: vercodeType));
  }

  static void sayHello(BuildContext context,
      {required String channelID, String? vercode, String? groupNo}) async {
    var remark =
        context.l10n.sayHelloRemark(CacheHelper.userProfile?.name ?? '');
    if (groupNo != null) {
      var groupInfo = await WKIM.shared.channelManager
          .getChannel(groupNo, WKChannelType.group);
      if (groupInfo != null) {
        remark = context.l10n.sayHelloGroupRemark(
            groupInfo.channelName, CacheHelper.userProfile?.name ?? '');
      }

      if (vercode?.isEmpty ?? true) {
        var member = await WKIM.shared.channelMemberManager
            .getMember(groupNo, WKChannelType.group, channelID);
        if (member?.vercode?.isNotEmpty ?? false) {
          vercode = member?.vercode;
        }
      }
    }

    Get.to(
        SayHelloPage(channelId: channelID, vercode: vercode, remark: remark));
  }

  /// 跳转到应用商店
  static Future<void> launchAppStore() async {
    String url;

    if (Platform.isAndroid) {
      // Google Play 商店
      final packageInfo = await PackageInfo.fromPlatform();
      url =
          "https://play.google.com/store/apps/details?id=${packageInfo.appName}";
    } else if (Platform.isIOS) {
      // App Store
      url = "https://apps.apple.com/app/id${CommonKeys.iosAppId}";
    } else {
      return; // 其他平台不处理
    }

    // 尝试打开 URL
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } else {
      debugPrint("无法打开商店页面: $url");
    }
  }

  static restartApp() {
    Restart.restartApp(
      notificationTitle: globalContext?.l10n.restartApp,
      notificationBody: globalContext?.l10n.restartAppTip,
    );
  }
}
