import 'package:json_annotation/json_annotation.dart';

part 'apply_friend_request.g.dart';

@JsonSerializable()
class ApplyFriendRequest {
  @J<PERSON><PERSON>ey(name: 'to_uid', defaultValue: '')
  final String toUid;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: '')
  final String remark;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: '')
  final String vercode;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'vercode_type')
  final int? vercodeType;

  const ApplyFriendRequest({
    required this.toUid,
    required this.remark,
    required this.vercode,
    this.vercodeType,
  });

  factory ApplyFriendRequest.fromJson(Map<String, dynamic> json) =>
      _$ApplyFriendRequestFromJson(json);

  Map<String, dynamic> toJson() => _$ApplyFriendRequestToJson(this);
}
