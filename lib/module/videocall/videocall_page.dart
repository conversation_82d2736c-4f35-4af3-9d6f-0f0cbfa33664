import 'dart:math';

import 'package:and/cache/cache_helper.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/module/videocall/widget/avatar_ripple_animation.dart';
import 'package:and/module/videocall/widget/call_banner.dart';
import 'package:and/module/videocall/widget/participant_view.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/type/const.dart';

import 'videocall_argument.dart';
import 'videocall_logic.dart';

class VideoCallPage extends StatefulWidget {
  final VideoCallArgument argument;

  const VideoCallPage({super.key, required this.argument});

  @override
  State<VideoCallPage> createState() => _VideoCallPageState();
}

class _VideoCallPageState extends State<VideoCallPage> {
  late final VideoCallLogic logic;
  late final VideoCallArgument argument;

  @override
  void initState() {
    super.initState();
    logic = Get.put<VideoCallLogic>(VideoCallLogic());
    argument = widget.argument;

    if (argument.callType == CallType.video) {
      logic.isLocalVideoEnabled.value = true;
    }
    if (argument.callUIState != null) {
      VideoCallLogic.uiState.value = argument.callUIState!;

      /// 发起通话邀请中 自己先进入房间
      if (VideoCallLogic.uiState.value == VideoCallUIState.calling) {
        _initializeCall();
      } else if (VideoCallLogic.uiState.value == VideoCallUIState.receiving) {
        logic.displayMode.value = DisplayMode.banner;
      }
    }
  }

  @override
  void dispose() {
    // 先调用 leaveRoom 方法
    logic.leaveRoom();
    
    // 然后删除控制器实例
    Get.delete<VideoCallLogic>();
    
    // 最后调用父类的 dispose 方法
    super.dispose();
  }

  Future<void> _initializeCall() async {
    if (argument.roomId != null && argument.livekitToken != null) {
      await logic.initializeCall();
      final joined =
          await logic.joinRoom(argument.roomId!, argument.livekitToken!);
      if (joined) {
        logic.joinCall(argument, isInviter: true);
      } else {
        VideoCallLogic.callToast(Get.context!.l10n.joinCallFailed);
        logic.cancelCall(argument);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final size = MediaQuery.of(context).size;
      Size windowSize;
      Offset position = Offset.zero;
      switch (logic.displayMode.value) {
        case DisplayMode.banner:
          double scale = CacheHelper.caringModel
              ? CacheHelper.fontScaleFactor
              : 1.0;
          scale = max(scale, 1);
          windowSize = Size(size.width, logic.bannerHeight * scale);
          position = Offset(0, MediaQuery.of(context).padding.top);
          break;
        case DisplayMode.fullscreen:
          windowSize = size;
          FocusScope.of(context).unfocus();
          break;
        case DisplayMode.floating:
          double safeAreaHeight = MediaQuery.of(context).padding.top +
              MediaQuery.of(context).padding.bottom;
          windowSize = Size(logic.floatingSize.width,
              logic.floatingSize.height + safeAreaHeight);
          position = logic.floatingPosition.value;
          break;
      }

      return Positioned(
          left: position.dx,
          top: position.dy,
          width: windowSize.width,
          height: windowSize.height,
          child: GestureDetector(
            onPanUpdate: _onDrag,
            onPanEnd: _onDragEnd,
            child: Container(
              color: logic.displayMode.value == DisplayMode.fullscreen
                  ? Colors.black
                  : Colors.transparent,
              width: windowSize.width,
              height: windowSize.height,
              child: Obx(() {
                switch (VideoCallLogic.uiState.value) {
                  case VideoCallUIState.calling:
                    return _buildCallingUI();
                  case VideoCallUIState.receiving:
                    return _buildReceivingUI();
                  case VideoCallUIState.connected:
                    return _buildConnectedUI();
                }
              }),
            ),
          ));
    });
  }

  void _onDrag(DragUpdateDetails details) {
    setState(() {
      final size = MediaQuery.of(context).size;
      final newDx = logic.floatingPosition.value.dx + details.delta.dx;
      final newDy = logic.floatingPosition.value.dy + details.delta.dy;
      double safeAreaHeight = MediaQuery.of(context).padding.top +
          MediaQuery.of(context).padding.bottom;
      // 确保悬浮窗不会超出屏幕边界
      logic.updateFloatingPosition(
        Offset(
          newDx.clamp(0, size.width - logic.floatingSize.width),
          newDy.clamp(
              0, size.height - logic.floatingSize.height - safeAreaHeight),
        ),
      );
    });
  }

  void _onDragEnd(DragEndDetails details) {
    // 自动吸附
    final screenWidth = MediaQuery.of(context).size.width;
    final x = logic.floatingPosition.value.dx;
    setState(() {
      if (x < screenWidth / 2) {
        logic.floatingPosition.value =
            Offset(10, logic.floatingPosition.value.dy); // 吸附左边
      } else {
        logic.floatingPosition.value = Offset(
            screenWidth - logic.floatingSize.width - 10,
            logic.floatingPosition.value.dy); // 吸附右边
      }
    });
  }

  Widget _buildCallingUI() {
    return Stack(
      children: [
        Center(
          child: AvatarRippleAnimation(
            rippleColor: Colors.white,
            child: AvatarWidget(CommonHelper.getAvatarUrl(
                argument.channel.channelID,
                channelType: argument.channel.channelType),
              name: argument.channel.displayName,),
          ),
        ),
        Positioned(
          left: 0,
          right: 0,
          top: MediaQuery.of(context).size.height / 2.0 + 70,
          child: Column(
            children: [
              Text(
                argument.channel.displayName,
                style: const TextStyle(color: Colors.white, fontSize: 24),
              ),
              const SizedBox(height: 10),
              Text(
                argument.callType == CallType.video
                    ? context.l10n.videoCallWaiting
                    : context.l10n.audioCallWaiting,
                style: const TextStyle(color: Colors.white70, fontSize: 16),
              ),
            ],
          ),
        ),
        Positioned(
          bottom: 60,
          left: 0,
          right: 0,
          child: Center(
            child: GestureDetector(
              onTap: () {
                logic.cancelCall(argument);
              },
              child: Container(
                width: 60,
                height: 60,
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child:
                    const Icon(Icons.call_end, color: Colors.white, size: 30),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildReceivingUI() {
    return Obx(() {
      switch (logic.displayMode.value) {
        case DisplayMode.fullscreen:
          return _buildFullModelView();
        case DisplayMode.banner:
          return _buildBanner();
        case DisplayMode.floating:
          return _buildFloatingMode();
      }
    });
  }

  Widget _buildFullModelView() {
    return Stack(
      children: [
        Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AvatarWidget(CommonHelper.getAvatarUrl(argument.channel.channelID,
                  channelType: argument.channel.channelType),
                name: argument.channel.displayName,),
              const SizedBox(height: 20),
              Text(
                argument.channel.displayName,
                style: const TextStyle(color: Colors.white, fontSize: 24),
              ),
              const SizedBox(height: 10),
              Text(
                argument.callType == CallType.video
                    ? context.l10n.videoCallIncoming
                    : context.l10n.audioCallIncoming,
                style: const TextStyle(color: Colors.white70, fontSize: 16),
              ),
            ],
          ),
        ),
        Positioned(
          bottom: 50,
          left: 0,
          right: 0,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildActionButton(
                color: Colors.red,
                icon: Icons.call_end,
                onTap: () {
                  logic.rejectCall(argument);
                },
                label: context.l10n.videoCallReject,
              ),
              const SizedBox(width: 80),
              _buildActionButton(
                color: Colors.blue,
                icon: argument.callType == CallType.audio
                    ? Icons.call
                    : Icons.videocam,
                onTap: () async {
                  await logic.joinCall(argument);
                },
                label: context.l10n.videoCallAccept,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBanner() {
    return Align(
      alignment: Alignment.topCenter,
      child: Padding(
        padding: const EdgeInsets.only(top: 20, left: 20, right: 20),
        child: CallBanner(
          argument: argument,
          onAccept: () async {
            await logic.joinCall(argument);
          },
          onReject: () {
            logic.rejectCall(argument);
          },
          onTap: () {
            logic.displayMode.value = DisplayMode.fullscreen;
          },
        ),
      ),
    );
  }

  Widget _buildConnectedUI() {
    return SafeArea(child: Obx(() {
      switch (logic.displayMode.value) {
        case DisplayMode.fullscreen:
          return Column(
            children: [
              // 顶部控制栏
              _buildTopBar(),
              // 用户视图
              Expanded(
                child: _buildUsersArea(),
              ),
              // 底部控制栏
              _buildBottomBar(),
            ],
          );
        case DisplayMode.banner:
          return Container(); // 通话中没有banner模式
        case DisplayMode.floating:
          // 悬浮窗模式
          return _buildFloatingMode();
      }
    }));
  }

  Widget _buildFloatingMode() {
    return Container(
      width: logic.floatingSize.width,
      height: logic.floatingSize.height,
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(51),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // 远程用户视图
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: logic.participants.isNotEmpty
                  ? ParticipantView(
                      participant: logic.participants.first,
                      isLocal: false,
                      isVideoCall: widget.argument.callType == CallType.video,
                      displayName: logic.participants.first.name,
                      channelId: widget.argument.channel.channelID,
                      channelType: widget.argument.channel.channelType,
                    )
                  : Container(color: Colors.transparent),
            ),
          ),
          // 点击切换回全屏
          Positioned.fill(
            child: GestureDetector(
              onTap: logic.toggleDisplayMode,
              child: Container(color: Colors.transparent),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsersArea() {
    return Obx(() {
      final participants = logic.participants;
      final totalParticipants =
          participants.length + 1; // +1 for local participant

      // 两人通话时使用上下布局
      // Handle case when there are no participants yet
      if (participants.isEmpty) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }

      if (totalParticipants == 2) {
        return Column(
          children: [
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: ParticipantView(
                  participant: participants[0],
                  isLocal: false,
                  isVideoCall: argument.callType == CallType.video,
                  displayName: participants[0].name,
                  channelId: argument.channel.channelID,
                  channelType: argument.channel.channelType,
                ),
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: _buildLocalParticipant(),
              ),
            ),
          ],
        );
      }

      // 多人通话时使用网格布局
      return GridView.builder(
        padding: const EdgeInsets.all(8.0),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 8.0,
          mainAxisSpacing: 8.0,
        ),
        itemCount: totalParticipants,
        itemBuilder: (context, index) {
          if (index == 0) {
            return ParticipantView(
              participant: logic.localParticipants.first,
              isLocal: true,
              isVideoCall: argument.callType == CallType.video,
              displayName: context.l10n.me,
              channelId: CacheHelper.uid ?? "",
              channelType: argument.channel.channelType,
            );
          } else {
            // 远程参与者
            final participant = participants[index - 1];
            return ParticipantView(
              participant: participant,
              isLocal: false,
              isVideoCall: argument.callType == CallType.video,
              displayName: participant.identity,
              channelId: argument.channel.channelID,
              channelType: argument.channel.channelType,
            );
          }
        },
      );
    });
  }

  Widget _buildLocalParticipant() {
    if (logic.localParticipants.isEmpty) {
      return Container(
        decoration: BoxDecoration(
          color: Colors.grey[900],
          borderRadius: BorderRadius.circular(12.0),
        ),
        child: Stack(
          fit: StackFit.expand,
          children: [
            Center(
              child: AvatarWidget(
                CommonHelper.getAvatarUrl(
                  CacheHelper.uid ?? '',
                  channelType: WKChannelType.personal,
                ),
                name: CacheHelper.userProfile?.name,
                size: 120,
                fontSize: 35,
              ),
            ),
            // 用户名和麦克风状态
            Positioned(
              left: 8,
              bottom: 8,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  children: [
                    Text(
                      context.l10n.me,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const Center(
              child: CircularProgressIndicator(),
            )
          ],
        ),
      );
    } else {
      return ParticipantView(
        participant: logic.localParticipants[0],
        isLocal: true,
        isVideoCall: argument.callType == CallType.video,
        displayName: context.l10n.me,
        channelId: CacheHelper.uid ?? "",
        channelType: WKChannelType.personal,
      );
    }
  }

  Widget _buildTopBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.black.withAlpha(179),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            icon: Obx(() => logic.displayMode.value == DisplayMode.fullscreen
                ? Icon(Icons.fullscreen_exit, color: Colors.white)
                : Container()),
            onPressed: () {
              logic.displayMode.value =
                  logic.displayMode.value == DisplayMode.fullscreen
                      ? DisplayMode.floating
                      : DisplayMode.fullscreen;
            },
          ),
          // 通话时长显示
          Obx(() => Text(
                logic.getFormattedCallDuration(),
                style: const TextStyle(color: Colors.white, fontSize: 16),
              )),
          // 屏幕共享按钮
          IconButton(
            icon: const Icon(null, color: Colors.white), //Icons.screen_share
            onPressed: logic.shareScreen,
          ),
        ],
      ),
    );
  }

  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.black.withAlpha(179),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildControlButton(
            icon: Obx(() => Icon(
                  logic.isLocalAudioEnabled.value ? Icons.mic : Icons.mic_off,
                  color: Colors.white,
                )),
            onPressed: logic.toggleLocalAudio,
          ),
          if (argument.callType == CallType.audio) ...[
            _buildControlButton(
              icon: const Icon(Icons.call_end, color: Colors.white),
              backgroundColor: Colors.red,
              onPressed: () {
                logic.hangupCall(argument);
              },
            ),
            _buildControlButton(
              icon: Obx(() => Icon(
                    logic.isSpeakerEnabled.value
                        ? Icons.volume_up
                        : Icons.volume_off,
                    color: Colors.white,
                  )),
              onPressed: logic.toggleSpeaker,
            ),
          ] else ...[
            _buildControlButton(
              icon: Obx(() => Icon(
                    logic.isSpeakerEnabled.value
                        ? Icons.volume_up
                        : Icons.volume_off,
                    color: Colors.white,
                  )),
              onPressed: logic.toggleSpeaker,
            ),
            _buildControlButton(
              icon: const Icon(Icons.call_end, color: Colors.white),
              backgroundColor: Colors.red,
              onPressed: () {
                logic.hangupCall(argument);
              },
            ),
          ],
          if (argument.callType == CallType.video) ...[
            _buildControlButton(
              icon: Obx(() => Icon(
                    logic.isLocalVideoEnabled.value
                        ? Icons.videocam
                        : Icons.videocam_off,
                    color: Colors.white,
                  )),
              onPressed: logic.toggleLocalVideo,
            ),
            _buildControlButton(
              icon: const Icon(Icons.switch_camera, color: Colors.white),
              onPressed: logic.switchCamera,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required Color color,
    required IconData icon,
    required VoidCallback onTap,
    required String label,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        GestureDetector(
          onTap: onTap,
          child: Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: Colors.white, size: 30),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: const TextStyle(color: Colors.white, fontSize: 14),
        ),
      ],
    );
  }

  Widget _buildControlButton({
    required Widget icon,
    Color? backgroundColor,
    required VoidCallback onPressed,
  }) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: backgroundColor ?? Colors.grey[800],
      ),
      child: IconButton(
        icon: icon,
        onPressed: onPressed,
      ),
    );
  }
}
