import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:flutter/material.dart';

import '../videocall_argument.dart';

class CallBanner extends StatelessWidget {
  final VideoCallArgument argument;
  final VoidCallback onAccept;
  final VoidCallback onReject;
  final VoidCallback onTap;

  const CallBanner({
    super.key,
    required this.argument,
    required this.onAccept,
    required this.onReject,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.black.withAlpha(230),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            AvatarWidget(
              CommonHelper.getAvatarUrl(
                argument.channel.channelID,
                channelType: argument.channel.channelType,
              ),
              name: argument.channel.displayName,
              size: 40,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    argument.channel.displayName,
                    style: const TextStyle(color: Colors.white, fontSize: 16),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis, // 添加文本溢出处理
                  ),
                  const SizedBox(height: 4),
                  Text(
                    argument.callType == CallType.video
                        ? context.l10n.videoCallIncoming
                        : context.l10n.audioCallIncoming,
                    style: const TextStyle(color: Colors.white70, fontSize: 14),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis, // 添加文本溢出处理
                  ),
                ],
              ),
            ),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildActionButton(
                  color: Colors.red,
                  icon: Icons.call_end,
                  onTap: onReject,
                ),
                const SizedBox(width: 12),
                _buildActionButton(
                  color: Colors.blue,
                  icon: argument.callType == CallType.audio
                      ? Icons.call
                      : Icons.videocam,
                  onTap: onAccept,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required Color color,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: color,
          shape: BoxShape.circle,
        ),
        child: Icon(icon, color: Colors.white, size: 24),
      ),
    );
  }
}
