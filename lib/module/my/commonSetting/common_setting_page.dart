import 'dart:io';

import 'package:and/cache/cache_helper.dart';
import 'package:and/common/utils/easy_loading_helper.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/module/appupgrade/app_upgrade_utils.dart';
import 'package:and/module/caringmode/caring_mode_page.dart';
import 'package:and/module/developer/developer_tool_widget.dart';
import 'package:and/module/user/mine/my_setting_manager.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/fileUtils.dart';
import 'package:and/utils/format_utils.dart';
import 'package:and/widget/simple_setting_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:package_info_plus/package_info_plus.dart';

class CommonSettingPage extends StatefulWidget {
  const CommonSettingPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _CommonSettingPageState();
  }
}

class _CommonSettingPageState extends State<CommonSettingPage> {
  String _appVersion = '';
  String _cacheSize = '0KB';

  @override
  void initState() {
    super.initState();
    _loadPackageInfo();
    _calculateCacheSize();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(context.l10n.generalSettings),
          actions: [
            DeveloperToolWidget(),
          ],
        ),
        body: _buildContent());
  }

  Widget _buildContent() {
    return Column(
      children: [_buildBaseInfo()],
    );
  }

  Widget _buildBaseInfo() {
    final List<Widget> items = [
      SimpleSettingItemWidget(context.l10n.clearCacheMemory, value: _cacheSize,
          onTap: () {
        _clearCache();
      }),

      SimpleSettingItemWidget(context.l10n.caringMode,
          value: CacheHelper.caringModel ? context.l10n.caringTurnOn : "",
          onTap: () {
        Get.to(CaringModePage())?.then((_) {
          // 当从关怀模式页面返回时，刷新UI
          setState(() {});
        });
      }),

      SimpleSettingItemWidget(context.l10n.mulitLanguage, onTap: () {
        Get.toNamed(RouteGet.language);
      }),
      
      // 添加不允许通过手机号搜索到我的开关
      SimpleSettingItemWidget(
        context.l10n.allowSearchByPhone,
        rightWidget: Switch(
          value: MySettingManager.instance.searchByPhone,
          onChanged: (value) {
            setState(() {
              MySettingManager.instance.setSearchByPhone(value);
            });
          },
        ),
      ),
      
      // 添加不允许通过短号搜索到我的开关
      SimpleSettingItemWidget(
        context.l10n.allowSearchByShort,
        rightWidget: Switch(
          value: MySettingManager.instance.searchByShort,
          onChanged: (value) {
            setState(() {
              MySettingManager.instance.setSearchByShort(value);
            });
          },
        ),
      ),

      SimpleSettingItemWidget(context.l10n.versionInfo, value: _appVersion,
          onTap: () {
            EasyLoadingHelper.show(onAction: () async {
              await AppUpgradeUtils.checkAppUpdateSetting(context);
            });
          }),

      SizedBox(height: 12),

      // 退出登录按钮
      Padding(
        padding: const EdgeInsets.all(20),
        child: OutlinedButton(
          style: OutlinedButton.styleFrom(
            foregroundColor: Colors.red,
            side: const BorderSide(color: Colors.red),
          ),
          onPressed: _confirmLogout,
          child:
              Text(context.l10n.logOut, style: const TextStyle(fontSize: 16)),
        ),
      ),
    ];

    return Expanded(
        child: ListView.builder(
            itemCount: items.length,
            itemBuilder: (context, index) {
              return items[index];
            }));
  }

  /// 确认退出登录
  void _confirmLogout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.l10n.makeSureLogout),
        content: Text(context.l10n.logoutTip),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(context.l10n.globalCancel),
          ),
          TextButton(
            onPressed: () {
              _logout();
            },
            child:
                Text(context.l10n.logOut, style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  Future<void> _logout() async {
    CommonHelper.exitLogin();
  }

  /// 获取应用版本信息
  Future<void> _loadPackageInfo() async {
    final packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      _appVersion = packageInfo.version;
    });
  }

  /// 获取缓存大小
  Future<int> _getCacheSize() async {
    int totalSize = 0;
    final cacheDir = await FileUtils.getCacheImageDirectory();
    final chatDownload = FileUtils.getChatDownloadDirectory();
    final wkvideo = FileUtils.getWKVideoDirectory();
    final wkimage = FileUtils.getChatDownloadDirectory();

    totalSize += await _getDirectoryFilesSize(cacheDir);
    totalSize += await _getDirectoryFilesSize(chatDownload);
    totalSize += await _getDirectoryFilesSize(wkvideo);
    totalSize += await _getDirectoryFilesSize(wkimage);

    return totalSize;
  }

  /// 获取文件夹目录下所有文件的大小
  Future<int> _getDirectoryFilesSize(Directory dic) async {
    int totalSize = 0;
    try {
      final cacheDir = await FileUtils.getCacheImageDirectory(); // 获取缓存目录
      if (await cacheDir.exists()) {
        await for (var entity in cacheDir.list(recursive: true)) {
          if (entity is File) {
            totalSize += await entity.length();
          }
        }
      }
    } catch (e) {
      print("缓存目录不存在: $e");
    }
    return totalSize;
  }

  /// 计算缓存大小
  Future<void> _calculateCacheSize() async {
    final size = await _getCacheSize();
    final formattedSize = FormatUtils.formatFileSize(size);

    if (mounted) {
      setState(() {
        _cacheSize = formattedSize;
      });
    }
  }

  /// 清空缓存
  Future<void> _clearCache() async {
    await DefaultCacheManager().emptyCache();
    final cacheDir = await FileUtils.getCacheImageDirectory();
    final chatDownload = await FileUtils.getChatDownloadDirectory();
    final wkvideo = await FileUtils.getWKVideoDirectory();
    final wkimage = await FileUtils.getChatDownloadDirectory();
    cacheDir.delete(recursive: true);
    chatDownload.delete(recursive: true);
    wkvideo.delete(recursive: true);
    wkimage.delete(recursive: true);
    setState(() {
      _cacheSize = '0KB';
      showToast(context.l10n.clearSuccess);
    });
  }
}
