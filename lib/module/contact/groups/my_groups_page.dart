import 'package:and/l10n/l10n.dart';
import 'package:and/module/chat/chat_page.dart';
import 'package:and/module/contact/widget/ui_channel_item.dart';
import 'package:and/widget/refresh/load_state.dart';
import 'package:and/widget/refresh/refresh_page.dart';
import 'package:and/widget/refresh/refresh_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';

import 'my_groups_logic.dart';


class MyGroupsPage extends StatefulWidget {
  const MyGroupsPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _MyGroupsPageState();
  }
}

class _MyGroupsPageState extends RefreshState<MyGroupsPage> {
  var logic = Get.put<MyGroupsLogic>(MyGroupsLogic());
  late RxList<WKChannel> list = logic.list;

  @override
  Rx<FitLoadStatus> getLoadState() {
    return logic.loadStatus;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
        appBar: AppBar(
          title: Text(context.l10n.savedGroups),
        ),
        body: Obx(() => _buildRefreshList()));
  }

  Widget _buildRefreshList() {
    return buildRefreshWidget(
      refreshController: refreshController,
      loadStatus: logic.loadStatus.value,
      hasData: logic.list.isNotEmpty,
      builder: (physics) => _buildList(physics),
      onRefresh: () {
        logic.refreshData();
      },
    );
  }

  Widget _buildList(ScrollPhysics? physics) {
    return CustomScrollView(
      physics: physics,
      slivers: [
        SliverPadding(
            padding: const EdgeInsets.symmetric(vertical: 15),
            sliver: SliverList(
              delegate:
              SliverChildBuilderDelegate((BuildContext context, int index) {
                WKChannel channel = list[index];
                return UiChannelItem(
                  channel: channel,
                  onTap: () {
                    ChatPage.open(
                        channelID: channel.channelID,
                        channelType: channel.channelType);
                  },
                );
              }, childCount: list.length),
            ))
      ],
    );
  }
}
