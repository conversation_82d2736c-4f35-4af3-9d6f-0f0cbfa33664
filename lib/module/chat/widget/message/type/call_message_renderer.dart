import 'dart:convert';

import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/constant/wk_message_content_type_ext.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/module/chat/widget/ui_msg_item.dart';
import 'package:and/module/videocall/videocall_argument.dart';
import 'package:flutter/material.dart';
import 'package:and/model/content/wk_call_content.dart';
import 'package:and/module/chat/widget/message/type/message_content_renderer.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';

class CallMessageRenderer extends MessageContentRenderer {
  @override
  Widget render(BuildContext context, WKMsg msg, MessageItemCallback callback) {
    return Container(
      constraints: const BoxConstraints(maxWidth: 200),
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: CallContentWidget(
        msg: msg,
      ),
    );
  }

  @override
  bool needContainer() {
    return true;
  }

  @override
  bool canRender(WKMsg msg) {
    return WkMessageContentTypeExt.isCallMsg(msg.contentType);
  }
}

class CallContentWidget extends StatefulWidget {
  final WKMsg msg;

  const CallContentWidget({super.key, required this.msg});

  @override
  State<StatefulWidget> createState() => _CallContentWidgetState();
}

class _CallContentWidgetState extends State<CallContentWidget> {
  late WkCallContent? callContent = widget.msg.messageContent as WkCallContent?;

  @override
  Widget build(BuildContext context) {
    if (callContent == null) return const SizedBox();

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 通话类型图标
        Icon(
          callContent!.callType == CallType.audio.value
              ? Icons.call
              : Icons.videocam,
          size: 20,
          color: Colors.black,
        ),
        const SizedBox(width: 8),
        // 通话状态文案
        Flexible(
          child: Text(
            _getCallContent(),
            style: TextStyles.fontSize15Normal,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        // 通话时长（仅在通话结束时显示）
        if (callContent!.type == -103 && callContent!.duration != null) ...[  
          const SizedBox(width: 8),
          Text(
            '${callContent!.duration! ~/ 60}:${(callContent!.duration! % 60).toString().padLeft(2, '0')}',
            style: const TextStyle(color: Colors.black),
          ),
        ],
      ],
    );
  }

  String _getCallContent() {
    switch (callContent!.type) {
      case -102:
        return callContent!.sourceUid == CacheHelper.uid
            ? context.l10n.callCanceled
            : context.l10n.callCanceledByPeer;
      case -103:
        return context.l10n.callDuration;
      case -104:
        return callContent!.sourceUid == CacheHelper.uid
            ? context.l10n.callNoAnswer
            : context.l10n.callMissed;
      case -105:
        return callContent!.sourceUid == CacheHelper.uid
            ? context.l10n.callRejected
            : context.l10n.callReject;
      default:
        return context.l10n.callMissed;
    }
  }
}
