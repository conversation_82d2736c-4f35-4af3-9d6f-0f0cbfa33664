import 'dart:async';
import 'dart:io';

import 'package:and/app.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/constant/file_dir_keys.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/content/wk_file_content.dart';
import 'package:and/model/extension/wk_file_content_ext.dart';
import 'package:and/module/chat/widget/message/type/text_message_renderer.dart';
import 'package:and/module/chat/widget/ui_msg_item.dart';
import 'package:and/module/file/file_viewer_page.dart';
import 'package:and/module/video/video_page.dart';
import 'package:and/utils/download/chat_file_utils.dart';
import 'package:and/utils/download/download_manager.dart';
import 'package:and/utils/fileUtils.dart';
import 'package:and/utils/format_utils.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/widget/image/simple_pics_wiper.dart';
import 'package:background_downloader/background_downloader.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mime/mime.dart';
import 'package:oktoast/oktoast.dart';
import 'package:open_file/open_file.dart';
import 'package:wukongimfluttersdk/model/wk_message_content.dart';

class MessageTextPreviewWidget extends StatelessWidget {
  final int channelType;
  final String channelID;
  final WKMessageContent? messageContent;
  final MessageItemCallback callback;

  const MessageTextPreviewWidget({
    super.key,
    required this.channelType,
    required this.channelID,
    required this.messageContent,
    required this.callback,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () async {
          Get.back();
        },
        child: SafeArea(
            child: Container(
          padding: EdgeInsets.all(10),
          color: Colors.white,
          child: Center(
            child: SingleChildScrollView(
              child: TextContentWidget(
                channelID: channelID,
                channelType: channelType,
                messageContent: messageContent,
                callback: callback,
                textSize: 35,
              ),
            ),
          ),
        )));
  }
}
