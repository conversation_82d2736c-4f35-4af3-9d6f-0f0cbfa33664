import 'package:and/cache/cache_helper.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/content/wk_multi_forward_content.dart';
import 'package:and/model/extension/wk_multi_forward_content_ext.dart';
import 'package:and/module/chat/chat_logic.dart';
import 'package:and/module/chat/multiForward/multi_forward_logic.dart';
import 'package:and/module/chat/widget/message/common/prompt_group_time_widget.dart';
import 'package:and/module/chat/widget/message/common/prompt_new_day_widget.dart';
import 'package:and/module/chat/widget/message/common/prompt_new_msg_widget.dart';
import 'package:and/module/chat/widget/message/position/receiver_position_renderer.dart';
import 'package:and/module/chat/widget/multi_forward_item.dart';
import 'package:and/module/chat/widget/ui_msg_item.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/utils/time_utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/type/const.dart';

class PinnedMsgArgument {
  List<PinnedMsg> pinnedMsgs;

  PinnedMsgArgument({required this.pinnedMsgs});

  factory PinnedMsgArgument.fromGet() {
    return (Get.arguments as PinnedMsgArgument);
  }
}

class PinnedMsgPage extends StatefulWidget {
  const PinnedMsgPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _PinnedMsgPageState();
  }
}

class _PinnedMsgPageState extends State<PinnedMsgPage> {
  late PinnedMsgArgument argument = Get.arguments as PinnedMsgArgument;

  late final list = argument.pinnedMsgs.map((e) => e.wkMsg).toList();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: _buildUserInfo(),
        ),
        resizeToAvoidBottomInset: false,
        body: SafeArea(child: _buildChatList()));
  }

  Widget _buildUserInfo() {
    return Text(context.l10n.pinnedMessagesTitle(list.length),
        maxLines: 2, overflow: TextOverflow.ellipsis);
  }

  Widget _buildChatList() {
    return CustomScrollView(
      reverse: true,
      shrinkWrap: true,
      slivers: [
        SliverList(
          delegate:
              SliverChildBuilderDelegate((BuildContext context, int index) {
            return _buildMessageItem(index);
          }, childCount: list.length),
        )
      ],
    );
  }

  Widget _buildMessageItem(int index) {
    // 判断是否显示日期分隔符（按天分组）
    WKMsg item = list[index];
    bool isNewDay = index == list.length - 1;
    bool isNewTimeGroup = index == list.length - 1;
    if (index < list.length - 1) {
      WKMsg preItem = list[index + 1];
      isNewDay =
          !TimeUtils.isSameDayByTimestamp(item.timestamp, preItem.timestamp);
      isNewTimeGroup =
          !TimeUtils.isWithinTimeGroup(item.timestamp, preItem.timestamp);
    }

    return Column(
      children: [
        if (isNewDay) PromptNewDayWidget(timestamp: item.timestamp),
        if (isNewTimeGroup) PromptGroupTimeWidget(timestamp: item.timestamp),
        UiMsgItem(
            msg: item,
            messageItemCallback: MessageItemCallback(
                resendMessage: (WKMsg msg) {},
                reEditMessage: (WKMsg msg) {},
                extraWidget: (msg) {
                  return Padding(padding: EdgeInsets.only(top: 10),child: Image.asset(
                    ImagePath.ic_jump_msg,
                    width: 30,
                    height: 30,
                  ),);
                })),

      ],
    );
  }
}
