import 'package:and/common/res/text_styles.dart';
import 'package:and/utils/format_utils.dart';
import 'package:and/utils/image_path.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class ChatUnreadWidget extends StatelessWidget {
  final int unreadCount;
  final bool isLoading;
  final Function onTap;

  const ChatUnreadWidget(
      {super.key,
      required this.unreadCount,
      this.isLoading = false,
      required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Padding(
          padding: EdgeInsets.only(top: 10),
          child: _buildJump(context),
        ),
        if (unreadCount > 0)
          Positioned(
            left: 0,
            right: 0,
            top: 0,
            child: Center(
              child: Badge(
                isLabelVisible: unreadCount > 0,
                backgroundColor: Colors.red,
                textStyle: TextStyles.fontSize15Normal
                    .copyWith(fontSize: 15, color: Colors.white),
                label: Text(FormatUtils.formatUnreadCount(unreadCount)),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildJump(BuildContext context) {
    return InkWell(
      splashColor: Colors.transparent,
      onTap: () {
        onTap();
      },
      child: Container(
        alignment: Alignment.center,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.5),
              spreadRadius: 2,
              blurRadius: 5,
              offset: Offset(0, 3), // changes position of shadow
            ),
          ],
        ),
        width: 50,
        height: 50,
        child: isLoading
            ? SpinKitCircle(
                size: 30,
                color: Theme.of(context).colorScheme.primary,
              )
            : Image.asset(ImagePath.ic_msg_down, height: 25),
      ),
    );
  }
}
