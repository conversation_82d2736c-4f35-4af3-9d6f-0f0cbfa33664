import 'package:and/app.dart';
import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/material.dart';

class CaringModePage extends StatefulWidget {
  const CaringModePage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _CaringModePageState();
  }
}

class _CaringModePageState extends State<CaringModePage> {
  // 字体缩放比例，默认为1.4（原关怀模式的缩放比例）
  double _fontScaleFactor = CacheHelper.fontScaleFactor;
  bool _caringModeEnabled = CacheHelper.caringModel;
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(context.l10n.caringMode),
          centerTitle: true,
        ),
        body: SafeArea(
            child: Column(
          children: [
            Expanded(child: _buildContent()),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                children: [
                  Expanded(
                    child: SubmitButton(
                      text: _caringModeEnabled
                          ? context.l10n.turnOff
                          : context.l10n.turnOn,
                      textSize: 15,
                      onPressed: _toggleCaringMode,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 40),
          ],
        )));
  }

  // 切换关怀模式
  void _toggleCaringMode() async {
    setState(() {
      _caringModeEnabled = !_caringModeEnabled;
    });

    // 保存关怀模式状态
    CacheHelper.saveCaringModel(_caringModeEnabled);

    _updateFontScale();
  }
  
  // 更新字体缩放比例
  void _updateFontScale() {
    CacheHelper.saveFontScaleFactor(_fontScaleFactor);
    
    // 触发应用重建以应用新的字体大小
    AppState.setting.changeLocale?.call();
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          children: [
            const SizedBox(height: 30),
            Image.asset(
              ImagePath.ic_caring_mode,
              height: 60,
              color: DColor.primaryColor,
            ),
            const SizedBox(height: 20),
            Text(
              context.l10n.caringMode,
              style: TextStyles.fontSize18Normal.copyWith(
                fontSize: 24,
                color: DColor.primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 30),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withAlpha(26),
                    spreadRadius: 1,
                    blurRadius: 5,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.l10n.caringModeSummary,
                    style: TextStyles.fontSize18Normal,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    context.l10n.caringModeSummary1,
                    style: TextStyles.fontSize16Normal.copyWith(color: Colors.grey),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 30),
            _buildFontSizeSlider(),
            const SizedBox(height: 30),
            _buildFontSizePreview(),
            const SizedBox(height: 30),
          ],
        ),
      ),
    );
  }
  
  // 构建字体大小滑块
  Widget _buildFontSizeSlider() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(26),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                context.l10n.fontSize,
                style: TextStyles.fontSize16SemiBold,
              ),
              Text(
                '${(_fontScaleFactor * 100).toInt()}%',
                style: TextStyles.fontSize16Normal.copyWith(
                  color: DColor.primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              const Text('A', style: TextStyle(fontSize: 14)),
              Expanded(
                child: Slider(
                  value: _fontScaleFactor,
                  min: 0.9,
                  max: 1.8,
                  divisions: 9,
                  activeColor: DColor.primaryColor,
                  inactiveColor: Colors.grey.withAlpha(76),
                  onChanged: (value) {
                    setState(() {
                      _fontScaleFactor = value;
                    });
                  },
                  onChangeEnd: (value) {
                    _updateFontScale();
                  },
                ),
              ),
              const Text('A', style: TextStyle(fontSize: 24)),
            ],
          ),
        ],
      ),
    );
  }
  
  // 构建字体大小预览
  Widget _buildFontSizePreview() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(26),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            context.l10n.previewEffect,
            style: TextStyles.fontSize16SemiBold,
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.withAlpha(26),
              borderRadius: BorderRadius.circular(8),
            ),
            child: MediaQuery(
              data: MediaQuery.of(context).copyWith(
                textScaler: TextScaler.linear(_fontScaleFactor),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.l10n.chatMessagePreview,
                    style: TextStyles.fontSize15Normal,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    context.l10n.smallTextPreview,
                    style: TextStyles.fontSize12Normal,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
