import 'dart:async';

import 'package:and/app.dart';
import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/constant/group_setting_keys.dart';
import 'package:and/eventbus/contact_sync_event.dart';
import 'package:and/http/http_config.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/module/group/widget/ui_channel_member_avatar_widget.dart';
import 'package:and/module/user/user_info_page.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:and/widget/simple_setting_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

import 'chat_detail_logic.dart';

class ChatDetailArgument {
  String channelId;

  ChatDetailArgument({required this.channelId});

  factory ChatDetailArgument.fromGet() {
    return (Get.arguments as ChatDetailArgument);
  }

  String getTag() {
    return channelId;
  }

  static getTagFromGet() {
    return ChatDetailArgument.fromGet().getTag();
  }
}

class ChatDetailPage extends StatefulWidget {
  const ChatDetailPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _ChatDetailPageState();
  }

  static void open({required String channelId}) {
    Get.toNamed(RouteGet.chatDetail,
        preventDuplicates: false,
        arguments: ChatDetailArgument(
          channelId: channelId,
        ));
  }
}

class _ChatDetailPageState extends State<ChatDetailPage> {
  late final ChatDetailArgument argument = ChatDetailArgument.fromGet();
  late ChatDetailLogic logic =
      Get.find<ChatDetailLogic>(tag: argument.getTag());
  late final channel = logic.data;

  late StreamSubscription _contactStatusEventSubscription;

  @override
  void initState() {
    super.initState();

    _contactStatusEventSubscription =
        eventBus.on<ContactSyncEvent>().listen((event) {
      if (mounted) {
        logic.refreshData();
      }
    });
  }

  @override
  void dispose() {
    _contactStatusEventSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(title: _buildTitle()), body: Obx(() => _buildList()));
  }

  Widget _buildTitle() {
    return Text(context.l10n.chatInfo);
  }

  Widget _buildList() {
    return CustomScrollView(
      slivers: [
        SliverPadding(
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
            sliver: SliverToBoxAdapter(
              child: _buildMembers(),
            )),
        SliverToBoxAdapter(
          child: _buildGroupInfo(),
        ),
        SliverToBoxAdapter(
          child: Container(height: 6),
        ),
        SliverToBoxAdapter(
          child: _buildSearchHistory(),
        ),
        SliverToBoxAdapter(
          child: Container(height: 6),
        ),
        SliverToBoxAdapter(
          child: _buildClearHistory(),
        ),
      ],
    );
  }

  Widget _buildMembers() {
    return Row(
      children: [
        Column(
          children: [
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                UserInfoPage.open(
                    channelID: channel.value?.channelID ?? '', vercodeType: 7);
              },
              child: AvatarWidget(
                channel.value?.avatarUrl,
                name: channel.value?.displayName,
                size: 45,
                fontSize: 14,
              ),
            ),
            SizedBox(height: 10),
            Text(
              channel.value?.displayName ?? '',
              style: TextStyles.fontSize13Normal,
              maxLines: 1,
            ),
          ],
        ),
        SizedBox(width: 20),
        Visibility(
            visible: channel.value?.isSystemChannel == false,
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                logic.addGroupMembers();
              },
              child: Column(
                children: [
                  Image.asset(ImagePath.ic_chat_add, height: 45),
                  SizedBox(height: 30),
                ],
              ),
            ))
      ],
    );
  }

  Widget _buildSearchHistory() {
    return Container(
        color: Colors.white,
        child: SimpleSettingItemWidget(context.l10n.searchChatMsg, onTap: () {
          logic.searchHistory(context);
        }));
  }

  Widget _buildClearHistory() {
    return Container(
        color: Colors.white,
        child: SimpleSettingItemWidget(context.l10n.clearChatMsg, onTap: () {
          logic.clearHistory(context);
        }));
  }

  Widget _buildGroupInfo() {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          SimpleSettingItemWidget(context.l10n.msgRemind,
              rightWidget: Switch(
                  value: channel.value?.mute == 1,
                  onChanged: (value) async {
                    var result = await logic.updateUserSetting(
                        GroupSettingKeys.mute, value ? 1 : 0);
                    if (result) {
                      channel.value?.mute = value ? 1 : 0;
                      WKIM.shared.channelManager
                          .addOrUpdateChannel(channel.value!);
                    }
                  })),
          SimpleSettingItemWidget(context.l10n.msgTop,
              rightWidget: Switch(
                  value: channel.value?.top == 1,
                  onChanged: (value) async {
                    var result = await logic.updateUserSetting(
                        GroupSettingKeys.top, value ? 1 : 0);
                    if (result) {
                      channel.value?.top = value ? 1 : 0;
                      WKIM.shared.channelManager
                          .addOrUpdateChannel(channel.value!);
                    }
                  })),
          Visibility(
              visible: channel.value?.isSystemChannel == false,
              child: SimpleSettingItemWidget(context.l10n.report, onTap: () {
                Map<String, String> params = {
                  "channel_id": argument.channelId,
                  "channel_type": WKChannelType.personal.toString(),
                  "uid": CacheHelper.uid ?? '',
                  "token": CacheHelper.token ?? '',
                };
                CommonHelper.report(params);
              }))
        ],
      ),
    );
  }
}
