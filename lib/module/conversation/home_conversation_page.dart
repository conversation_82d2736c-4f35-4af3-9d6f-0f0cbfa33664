import 'dart:math';

import 'package:and/app.dart';
import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/module/chat/chat_page.dart';
import 'package:and/module/contact/widget/add_contact_menu_widget.dart';
import 'package:and/module/conversation/widget/ui_conversation_item.dart';
import 'package:and/module/search/globalSearch/global_search_page.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/utils/popmenu_util.dart';
import 'package:and/widget/connect_status_widget.dart';
import 'package:and/widget/refresh/load_state.dart';
import 'package:and/widget/refresh/refresh_page.dart';
import 'package:and/widget/refresh/refresh_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/entity/conversation.dart';
import 'package:wukongimfluttersdk/type/const.dart';

import 'home_conversation_logic.dart';

class HomeConversationPage extends StatefulWidget {
  const HomeConversationPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return HomeConversationPageState();
  }
}

class HomeConversationPageState extends RefreshState<HomeConversationPage>
    with RouteAware {
  final logic = Get.put<HomeConversationLogic>(HomeConversationLogic());
  late final list = logic.list;

  // 添加滚动控制器
  final ScrollController _scrollController = ScrollController();

  // 添加当前未读消息索引记录
  int _currentUnreadIndex = -1;

  @override
  void initState() {
    super.initState();
    // 初始化滚动控制器
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    routeObserver.unsubscribe(this);
    Get.delete<HomeConversationLogic>();
    super.dispose();
  }

  void _onScroll() {
    // 可以在这里添加滚动监听逻辑
  }

  // 滚动到下一个未读消息会话
  void scrollToNextUnreadConversation() {
    if (list.isEmpty) return;

    // 查找所有有未读消息的会话索引
    List<int> unreadIndices = [];
    for (int i = 0; i < list.length; i++) {
      if (list[i].unreadCount > 0) {
        unreadIndices.add(i);
      }
    }

    if (unreadIndices.isEmpty) {
      // 没有未读消息，滚动到顶部
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      _currentUnreadIndex = -1;
      return;
    }

    // 计算当前可视区域的起始和结束位置
    // 关怀模式只是放大字体间隙并没有放大
    double scale = CacheHelper.caringModel
        ? CacheHelper.fontScaleFactor
        : 1.0;
    scale = max(scale, 1);
    double itemHeight = scale * 32 + 38;
    double searchBarHeight = scale * 15 + 40;
    double viewportStart = _scrollController.offset + itemHeight;
    double viewportEnd =
        viewportStart + _scrollController.position.viewportDimension;

    // 查找当前可视区域内的未读消息会话
    List<int> visibleUnreadIndices = [];
    for (int index in unreadIndices) {
      double itemStart = index * itemHeight + searchBarHeight;
      double itemEnd = itemStart + itemHeight;

      // 判断item是否在可视区域内
      if (itemEnd > viewportStart && itemStart < viewportEnd) {
        visibleUnreadIndices.add(index);
      }
    }

    if (visibleUnreadIndices.isNotEmpty) {
      // 如果可视区域内有未读消息会话
      visibleUnreadIndices.sort((a, b) => a.compareTo(b));

      _currentUnreadIndex = _getNextIndex(visibleUnreadIndices);
    } else {
      _currentUnreadIndex = _getNextIndex(unreadIndices);
    }

    // 滚动到目标位置
    _scrollToIndex(_currentUnreadIndex);
  }

  int _getNextIndex(List<int> indices) {
    int nextIndex;
    if (_currentUnreadIndex == -1) {
      // 首次滚动，选择第一个未读消息
      nextIndex = indices[0];
    } else {
      // 查找当前索引在未读列表中的位置
      int currentPosition = indices.indexOf(_currentUnreadIndex);
      if (currentPosition == -1 || currentPosition == indices.length - 1) {
        // 当前索引不在未读列表中或已是最后一个，循环到第一个
        nextIndex = indices[0];
      } else {
        // 移动到下一个未读消息
        nextIndex = indices[currentPosition + 1];
      }
    }
    return nextIndex;
  }

  // 滚动到指定索引位置
  // 关怀模式只是放大字体间隙并没有放大
  void _scrollToIndex(int index) {
    if (index < 0 || index >= list.length) return;

    // 计算目标位置
    double scale = CacheHelper.caringModel
        ? CacheHelper.fontScaleFactor
        : 1.0;
    scale = max(scale, 1);
    double itemHeight = scale * 32 + 38;
    double searchBarHeight = scale * 15 + 40;
    final double offset = index * itemHeight + searchBarHeight;

    _scrollController.animateTo(
      offset,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
  }

  @override
  void didPopNext() {
    super.didPopNext();
    if (mounted) {
      logic.refreshData();
    }
  }

  @override
  Rx<FitLoadStatus> getLoadState() {
    return logic.loadStatus;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
        appBar: AppBar(
          title: _buildTitle(),
          titleSpacing: 20,
          actions: [
            Padding(
                padding: EdgeInsets.only(right: 15),
                child: AddContactMenuWidget())
          ],
        ),
        body: Obx(() => _buildContent()));
  }

  Widget _buildTitle() {
    return Row(children: [
      Text(context.l10n.tabbarConversations),
      Obx(() => _buildConnectStatus()),
    ]);
  }

  Widget _buildContent() {
    return Column(
      children: [
        ConnectStatusWidget(logic.status.value),
        Expanded(child: _buildRefreshList())
      ],
    );
  }

  Widget _buildConnectStatus() {
    if (CacheHelper.devTestMode) {
      if (logic.status.value == WKConnectStatus.success ||
          logic.status.value == WKConnectStatus.syncCompleted ||
          logic.statusStr.isEmpty) {
        return Container();
      }
      return Text(" (${logic.statusStr.value})",
          style: TextStyles.fontSize13Normal
              .copyWith(color: DColor.secondaryTextColor));
    }
    if (logic.status.value == WKConnectStatus.connecting ||
        logic.status.value == WKConnectStatus.syncMsg) {
      return Container(
          margin: EdgeInsets.only(left: 8.0),
          width: 10.0,
          height: 10.0,
          child: CircularProgressIndicator(
            backgroundColor: Colors.transparent,
            strokeWidth: 1,
            valueColor: AlwaysStoppedAnimation<Color>(DColor.primaryColor),
          ));
    }
    return Container();
  }

  Widget _buildRefreshList() {
    return buildRefreshWidget(
      refreshController: refreshController,
      loadStatus: logic.loadStatus.value,
      hasData: logic.list.isNotEmpty,
      builder: (physics) => _buildList(physics),
      onRefresh: () {
        logic.refreshData();
      },
    );
  }

  Widget _buildList(ScrollPhysics? physics) {
    return NotificationListener<ScrollNotification>(
        onNotification: (notification) {
          if (notification is ScrollStartNotification &&
              notification.dragDetails != null) {
            FocusScope.of(context).unfocus();
            SystemChannels.textInput.invokeMethod('TextInput.hide');
          }
          return true;
        },
        child: CustomScrollView(
          controller: _scrollController, // 使用滚动控制器
          physics: physics,
          keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
          slivers: [
            _buildSearchBar(),
            SliverPadding(
                padding: const EdgeInsets.symmetric(vertical: 15),
                sliver: SliverList(
                  delegate: SliverChildBuilderDelegate(
                      (BuildContext context, int index) {
                    WKUIConversationMsg item = list[index];
                    return UiConversationItem(
                      msg: item,
                      onTap: () {
                        ChatPage.open(
                            channelID: item.channelID,
                            channelType: item.channelType);
                      },
                      onLongPressTap: (details) async {
                        _onShowMenu(item, details);
                      },
                    );
                  }, childCount: list.length),
                ))
          ],
        ));
  }

  void _onShowMenu(
      WKUIConversationMsg item, LongPressStartDetails details) async {
    var channel = await item.getWkChannel();
    var isPin = channel?.top == 1;
    var isMute = channel?.mute == 1;
    if (channel == null) return;
    var items = [
      MenuItem(
          text: isMute
              ? context.l10n.openChannelNotice
              : context.l10n.closeChannelNotice,
          icon: isMute ? ImagePath.ic_msg_unmute : ImagePath.ic_msg_mute,
          onTap: () {
            logic.updateMute(channel.channelID, channel.channelType, !isMute);
          }),
      MenuItem(
          text: isPin ? context.l10n.msgCancelTop : context.l10n.msgSetTop,
          icon: isPin ? ImagePath.ic_msg_unpin : ImagePath.ic_msg_pin,
          onTap: () {
            logic.updatePin(channel.channelID, channel.channelType, !isPin);
          }),
      MenuItem(
          text: context.l10n.deleteChat,
          icon: ImagePath.ic_msg_delete,
          onTap: () {
            logic.deleteMsg(context, channel.channelID, channel.channelType);
          })
    ];
    await PopMenuUtil.showPopupMenu(context,
        Offset(details.globalPosition.dx, details.globalPosition.dy), items);
  }

  Widget _buildSearchBar() {
    return SliverToBoxAdapter(
      child: GestureDetector(
        onTap: () {
          GlobalSearchPage.open();
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            height: 40,
            width: 300,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.search, color: Colors.grey),
                const SizedBox(width: 10),
                Text(
                  context.l10n.search,
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 15),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
